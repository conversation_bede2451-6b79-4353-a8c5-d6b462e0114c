# TrackingImporter 性能优化指南

## 优化概述

针对30万条数据的处理，我们对 `trackingImporter.js` 进行了全面的性能优化，主要改进包括：

### 1. API调用优化
- **并发控制**：限制同时进行的API请求数量，避免服务器过载
- **重试机制**：指数退避重试，提高成功率
- **批次优化**：减小批次大小，提高单次请求成功率
- **超时控制**：设置合理的超时时间

### 2. 数据库操作优化
- **COPY命令**：使用PostgreSQL的COPY命令进行批量插入，比INSERT快5-10倍
- **回退机制**：COPY失败时自动回退到分批INSERT
- **事务优化**：减少事务开销
- **连接池优化**：更好的连接管理

### 3. 内存使用优化
- **统计数据**：只保留统计信息，不保存具体运单号列表
- **垃圾回收**：在适当时机强制垃圾回收
- **分页优化**：减小数据库分页大小，降低内存占用

### 4. 性能监控
- **实时监控**：API成功率、处理速度、内存使用
- **进度报告**：每30秒输出一次进度
- **最终报告**：完整的性能统计

## 性能提升预期

基于优化措施，预期性能提升：

| 优化项目 | 预期提升 | 说明 |
|---------|---------|------|
| API并发控制 | 30-50% | 减少API失败重试 |
| 数据库COPY | 500-1000% | COPY比INSERT快5-10倍 |
| 内存优化 | 60-80% | 减少内存占用和GC压力 |
| 整体处理速度 | 200-400% | 综合优化效果 |

## 配置调优建议

### 根据服务器性能调整参数

1. **API并发数** (`API_CONCURRENCY_LIMIT`)
   - 低配置服务器：2-3
   - 中等配置：5-8
   - 高配置服务器：8-15

2. **API批次大小** (`PROCESSING_BATCH_SIZE`)
   - 网络较差：50-100
   - 网络良好：100-200
   - 高速网络：200-500

3. **数据库批次** (`DB_BATCH_SIZE`)
   - 小内存服务器：1000-3000
   - 中等内存：5000-10000
   - 大内存服务器：10000-20000

### 环境变量配置

```bash
# 设置环境
export NODE_ENV=production

# 启用垃圾回收（可选）
node --expose-gc trackingImporter.js
```

## 监控和调试

### 性能监控输出示例
```
=== 性能监控 (运行时间: 120秒) ===
API调用: 150 (成功: 145, 失败: 5)
数据库操作: 12
已处理记录: 15000
内存使用: RSS=256MB, Heap=180/220MB
处理速度: 125 记录/秒
=======================================
```

### 关键指标说明
- **API成功率**：应保持在95%以上
- **处理速度**：目标100-500记录/秒
- **内存使用**：RSS应稳定，不持续增长
- **数据库操作**：频率适中，避免过于频繁

## 故障排除

### 常见问题及解决方案

1. **API频繁失败**
   - 减少并发数 (`API_CONCURRENCY_LIMIT`)
   - 增加重试延迟 (`API_RETRY_DELAY`)
   - 减小批次大小 (`PROCESSING_BATCH_SIZE`)

2. **内存持续增长**
   - 减少数据库批次大小 (`DB_BATCH_SIZE`)
   - 启用强制垃圾回收
   - 检查是否有内存泄漏

3. **数据库操作慢**
   - 检查数据库连接
   - 优化数据库索引
   - 调整批次大小

4. **处理速度慢**
   - 增加API并发数
   - 优化网络连接
   - 检查服务器资源使用

## 进一步优化建议

1. **数据库索引**：确保waybill_number字段有索引
2. **网络优化**：使用更快的网络连接
3. **服务器升级**：增加CPU和内存
4. **分布式处理**：多台服务器并行处理
5. **缓存机制**：缓存重复查询的结果

## 使用方法

1. 直接运行优化后的脚本：
   ```bash
   node trackingImporter.js
   ```

2. 使用自定义配置：
   ```bash
   NODE_ENV=production node --expose-gc trackingImporter.js
   ```

3. 监控运行状态，根据输出调整配置参数

## 注意事项

- 首次运行建议使用较小的配置参数测试
- 监控API服务器的响应，避免过载
- 定期检查数据库性能和存储空间
- 保持网络连接稳定

hoistPattern:
  - '*'
hoistedDependencies:
  asynckit@0.4.0:
    asynckit: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  combined-stream@1.0.8:
    combined-stream: private
  delayed-stream@1.0.0:
    delayed-stream: private
  dunder-proto@1.0.1:
    dunder-proto: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  follow-redirects@1.15.9:
    follow-redirects: private
  form-data@4.0.3:
    form-data: private
  function-bind@1.1.2:
    function-bind: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  gopd@1.2.0:
    gopd: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  pg-cloudflare@1.2.6:
    pg-cloudflare: private
  pg-connection-string@2.9.1:
    pg-connection-string: private
  pg-int8@1.0.1:
    pg-int8: private
  pg-pool@3.10.1(pg@8.16.2):
    pg-pool: private
  pg-protocol@1.10.2:
    pg-protocol: private
  pg-types@2.2.0:
    pg-types: private
  pgpass@1.0.5:
    pgpass: private
  postgres-array@2.0.0:
    postgres-array: private
  postgres-bytea@1.0.0:
    postgres-bytea: private
  postgres-date@1.0.7:
    postgres-date: private
  postgres-interval@1.2.0:
    postgres-interval: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  split2@4.2.0:
    split2: private
  xtend@4.0.2:
    xtend: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.6.0
pendingBuilds: []
prunedAt: Fri, 20 Jun 2025 07:49:39 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped: []
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120

// 性能优化配置文件
// 根据您的服务器性能和API限制调整这些参数

module.exports = {
  // API相关配置
  API: {
    // 并发限制：同时进行的API请求数量
    // 建议值：3-10，根据API服务器承受能力调整
    CONCURRENCY_LIMIT: 5,
    
    // 重试配置
    RETRY_ATTEMPTS: 3,      // 重试次数
    RETRY_DELAY: 1000,      // 初始重试延迟(ms)
    
    // 请求超时时间(ms)
    TIMEOUT: 30000,
    
    // 批次大小：每个API请求包含的运单号数量
    // 建议值：50-200，太大可能导致超时，太小效率低
    BATCH_SIZE: 100,
    
    // 批次组之间的延迟(ms)，避免API服务器过载
    BATCH_GROUP_DELAY: 500
  },

  // 数据库相关配置
  DATABASE: {
    // 数据库批量插入大小
    // 建议值：1000-10000，根据数据库性能调整
    BATCH_SIZE: 5000,
    
    // 数据库分页大小：每次从数据库读取的运单数量
    // 建议值：1000-10000，减少内存占用
    FETCH_SIZE: 5000,
    
    // 连接池配置
    POOL: {
      max: 10,              // 最大连接数
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    }
  },

  // 内存管理配置
  MEMORY: {
    // 是否启用强制垃圾回收
    FORCE_GC: true,
    
    // 内存监控间隔(ms)
    MONITOR_INTERVAL: 30000,
    
    // 内存使用警告阈值(MB)
    WARNING_THRESHOLD: 1024
  },

  // 日志配置
  LOGGING: {
    // 是否启用详细日志
    VERBOSE: true,
    
    // 是否启用性能监控
    PERFORMANCE_MONITOR: true,
    
    // 进度报告间隔(ms)
    PROGRESS_INTERVAL: 30000
  }
};

// 根据环境自动调整配置
const ENV_CONFIGS = {
  // 开发环境：较小的批次，更多日志
  development: {
    API: { CONCURRENCY_LIMIT: 2, BATCH_SIZE: 50 },
    DATABASE: { BATCH_SIZE: 1000, FETCH_SIZE: 1000 },
    LOGGING: { VERBOSE: true }
  },
  
  // 生产环境：优化性能
  production: {
    API: { CONCURRENCY_LIMIT: 8, BATCH_SIZE: 200 },
    DATABASE: { BATCH_SIZE: 10000, FETCH_SIZE: 10000 },
    LOGGING: { VERBOSE: false }
  },
  
  // 测试环境：小批次快速测试
  test: {
    API: { CONCURRENCY_LIMIT: 1, BATCH_SIZE: 10 },
    DATABASE: { BATCH_SIZE: 100, FETCH_SIZE: 100 },
    LOGGING: { VERBOSE: true }
  }
};

// 应用环境特定配置
const currentEnv = process.env.NODE_ENV || 'development';
if (ENV_CONFIGS[currentEnv]) {
  const envConfig = ENV_CONFIGS[currentEnv];
  Object.keys(envConfig).forEach(section => {
    if (module.exports[section]) {
      Object.assign(module.exports[section], envConfig[section]);
    }
  });
}

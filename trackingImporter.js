const axios = require('axios');
const { Pool } = require('pg');

function safeParseDate(dateString) {
  if (!dateString) return null;
  // 专门处理带'+'号的特殊格式
  const cleanDateString = dateString.split('+')[0];
  const date = new Date(cleanDateString);
  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    return null;
  }
  return date.toISOString();
}

// API配置
const API_URL = "http://api.track.yw56.com.cn/api/tracking";
const AUTH_TOKEN = "AD153B35-22E8-4F7F-B302-895664E28B80";



// 数据库配置
const DB_CONFIG = {
  host: "hgprecn-cn-9lb36j0qc009-cn-hangzhou-vpc-st.hologres.aliyuncs.com",
  port: 80,
  database: "ywwl_holo",
  user: "LTAI5tPZDr4xh8rBQF4oSraa",
  password: "******************************",
  table: "ods_pp_track_final_node_test"
};

async function fetchTrackingData(trackingNumbers) {
  try {
    const response = await axios.get(API_URL, {
      headers: {
        Authorization: AUTH_TOKEN
      },
      params: {
        nums: trackingNumbers.join(',')
      }
    });
    return response.data;
  } catch (error) {
    console.error('API请求失败:', error.message);
    throw error;
  }
}

function processData(apiData, trackingNumbersInBatch) {
  const records = [];
  const invalidTrackingNumbers = [];

  if (!apiData || !apiData.result || !Array.isArray(apiData.result)) {
    console.error('无效的API响应数据');
    invalidTrackingNumbers.push(...trackingNumbersInBatch);
    return { records, invalidTrackingNumbers };
  }

  const apiTrackingNumbers = new Set(apiData.result.map(item => item.tracking_number));

  trackingNumbersInBatch.forEach(num => {
    if (!apiTrackingNumbers.has(num)) {
      invalidTrackingNumbers.push(num);
    }
  });

  apiData.result.forEach(item => {
    if (!item || !item.tracking_status || !Array.isArray(item.checkpoints) || item.checkpoints.length === 0) {
      if (item && item.tracking_number && !invalidTrackingNumbers.includes(item.tracking_number)) {
        invalidTrackingNumbers.push(item.tracking_number);
      }
      return;
    }

    const trackingStatus = item.tracking_status;

    try {
      const matchingCheckpoints = item.checkpoints.filter(
        checkpoint => checkpoint && checkpoint.tracking_status === trackingStatus
      );

      if (matchingCheckpoints.length === 0) {
        if (item.tracking_number && !invalidTrackingNumbers.includes(item.tracking_number)) {
          invalidTrackingNumbers.push(item.tracking_number);
        }
        return;
      }

      records.push({
        tracking_number: item.tracking_number || '',
        waybill_number: item.waybill_number || '',
        exchange_number: item.exchange_number || '',
        tracking_status: trackingStatus,
        tracking_node_time: safeParseDate(matchingCheckpoints[0]?.time_stamp),
        tracking_node_time_create: safeParseDate(matchingCheckpoints[0]?.time_create)
      });
    } catch (error) {
      console.error('处理跟踪项时出错:', error.message);
      if (item.tracking_number && !invalidTrackingNumbers.includes(item.tracking_number)) {
        invalidTrackingNumbers.push(item.tracking_number);
      }
    }
  });

  return { records, invalidTrackingNumbers };
}

async function saveToDatabase(client, records) {
  if (records.length === 0) {
    return { insertedWaybills: [], updatedWaybills: [] };
  }

  // const waybillNumbers = records.map(r => r.waybill_number);

  try {
    // const checkResult = await client.query(
    //   `SELECT waybill_number FROM ${DB_CONFIG.table} WHERE waybill_number = ANY($1::text[])`,
    //   [waybillNumbers]
    // );
    // const existingWaybills = new Set(checkResult.rows.map(row => row.waybill_number));

    const recordsToInsert = [];
    // const recordsToUpdate = [];
    for (const record of records) {
      // if (existingWaybills.has(record.waybill_number)) {
        // recordsToUpdate.push(record);
      // } else {
        recordsToInsert.push(record);
      // }
    }

    await client.query('BEGIN');

    // 批量更新 (Bulk Update)
    // if (recordsToUpdate.length > 0) {
    //   const updateParams = [];
    //   const updateValues = recordsToUpdate.map((r, i) => {
    //     const base = i * 6;
    //     updateParams.push(
    //       r.tracking_number, r.waybill_number, r.exchange_number,
    //       r.tracking_status, r.tracking_node_time, r.tracking_node_time_create
    //     );
    //     return `($${base + 1}, $${base + 2}, $${base + 3}, $${base + 4}, $${base + 5}::timestamp, $${base + 6}::timestamp)`;
    //   }).join(',');

    //   const updateQuery = `
    //     UPDATE ${DB_CONFIG.table} AS t SET
    //       tracking_number = c.tracking_number,
    //       exchange_number = c.exchange_number,
    //       tracking_status = c.tracking_status,
    //       tracking_node_time = c.tracking_node_time,
    //       tracking_node_time_create = c.tracking_node_time_create
    //     FROM (VALUES ${updateValues}) AS c(tracking_number, waybill_number, exchange_number, tracking_status, tracking_node_time, tracking_node_time_create)
    //     WHERE t.waybill_number = c.waybill_number;
    //   `;
    //   await client.query(updateQuery, updateParams);
    // }

    // 批量插入 (Bulk Insert)
    if (recordsToInsert.length > 0) {
      const insertParams = [];
      const insertValues = recordsToInsert.map((r, i) => {
        const base = i * 6;
        insertParams.push(
          r.tracking_number, r.waybill_number, r.exchange_number,
          r.tracking_status, r.tracking_node_time, r.tracking_node_time_create
        );
        return `($${base + 1}, $${base + 2}, $${base + 3}, $${base + 4}, $${base + 5}::timestamp, $${base + 6}::timestamp)`;
      }).join(',');

      const insertQuery = `
        INSERT INTO ${DB_CONFIG.table} (
          tracking_number, waybill_number, exchange_number,
          tracking_status, tracking_node_time, tracking_node_time_create
        ) VALUES ${insertValues};
      `;
      await client.query(insertQuery, insertParams);
    }

    await client.query('COMMIT');
    
    const insertedWaybills = recordsToInsert.map(r => r.waybill_number);
    const updatedWaybills = []// recordsToUpdate.map(r => r.waybill_number);

    console.log(`数据库操作完成: 插入 ${insertedWaybills.length} 条, 更新 ${updatedWaybills.length} 条`);
    return { insertedWaybills, updatedWaybills };

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('数据库事务失败:', error.message);
    throw error;
  }
}

async function processBatch(batch, batchNum, totalBatches) {
  const batchStartTime = Date.now();
  console.log(`\n--- 开始处理批次 ${batchNum}/${totalBatches} (运单号数量: ${batch.length}) ---`);
  
  try {
    const apiData = await fetchTrackingData(batch);
    const { records, invalidTrackingNumbers } = processData(apiData, batch);
    
    console.log(`批次 ${batchNum} API处理完成，共 ${records.length} 条有效记录`);
    const batchEndTime = Date.now();
    console.log(`--- 批次 ${batchNum} 处理完毕，耗时 ${(batchEndTime - batchStartTime) / 1000} 秒 ---`);

    return {
      records,
      invalidTrackingNumbers
    };

  } catch (error) {
    console.error(`处理批次 ${batchNum} 时出错:`, error.message);
    const batchEndTime = Date.now();
    console.log(`--- 批次 ${batchNum} 处理失败，耗时 ${(batchEndTime - batchStartTime) / 1000} 秒 ---`);
    // 返回一个包含错误信息和无效运单号的结构，以便上层可以统一处理
    return {
      records: [],
      invalidTrackingNumbers: batch, // 整个批次的运单都标记为无效
      error: error.message
    };
  }
}

async function main() {
  const startTime = Date.now();
  const dbFetchSize = 10000; // 每次从数据库拉取的数量
  const processingBatchSize = 200; // 每个API请求批次的大小
  const dbInsertThreshold = 10000; // 数据库插入阈值
  
  const allInserted = [];
  const allUpdated = [];
  const allInvalid = [];
  let recordsBuffer = []; // 用于累积记录的缓冲区

  const pool = new Pool(DB_CONFIG);
  console.log('数据库连接池已创建。');
  let client; // 在循环外声明client

  try {
    client = await pool.connect(); // 获取一个长期连接
    console.log('数据库连接已获取。');

    let lastWaybillNumber = ''; // Keyset for pagination
    let pageCount = 1;

    while (true) {
      console.log(`\n\n---正在从数据库获取第 ${pageCount} 页运单 (LIMIT ${dbFetchSize})---`);
      
      let query;
      let queryParams;

      if (lastWaybillNumber === '') {
        // First page
        query = `SELECT waybill_number FROM public.ods_pp_track_final_node_waybill_number_test ORDER BY waybill_number LIMIT $1`;
        queryParams = [dbFetchSize];
      } else {
        // Subsequent pages using keyset pagination
        query = `SELECT waybill_number FROM public.ods_pp_track_final_node_waybill_number_test WHERE waybill_number > $1 ORDER BY waybill_number LIMIT $2`;
        queryParams = [lastWaybillNumber, dbFetchSize];
      }
      
      const result = await client.query(query, queryParams);
      const waybillsToProcess = result.rows.map(row => row.waybill_number);

      if (waybillsToProcess.length === 0) {
        console.log('数据库中已无更多运单，处理结束。');
        break; // 退出主循环
      }
      
      // 更新下一轮查询的起始点
      lastWaybillNumber = waybillsToProcess[waybillsToProcess.length - 1];

      console.log(`获取到 ${waybillsToProcess.length} 个运单号，开始分批处理...`);

      const batches = [];
      for (let i = 0; i < waybillsToProcess.length; i += processingBatchSize) {
          batches.push(waybillsToProcess.slice(i, i + processingBatchSize));
      }
      const totalBatchesInPage = batches.length;
      console.log(`本页运单将分为 ${totalBatchesInPage} 个批次进行API请求。`);

      const batchPromises = batches.map((batch, index) =>
        processBatch(batch, index + 1, totalBatchesInPage)
      );

      const results = await Promise.allSettled(batchPromises);

      for (const res of results) {
        if (res.status === 'fulfilled' && res.value) {
          const { records, invalidTrackingNumbers } = res.value;
          recordsBuffer.push(...records);
          allInvalid.push(...invalidTrackingNumbers);
        } else {
          console.error(`一个批次的Promise处理失败:`, res.reason || (res.value && res.value.error));
          if (res.value && res.value.invalidTrackingNumbers) {
            allInvalid.push(...res.value.invalidTrackingNumbers);
          }
        }

        // 检查缓冲区是否达到阈值
        if (recordsBuffer.length >= dbInsertThreshold) {
          console.log(`\n缓冲区达到 ${recordsBuffer.length} 条记录，开始写入数据库...`);
          const toInsert = recordsBuffer.splice(0, dbInsertThreshold);
          
          const dbResult = await saveToDatabase(client, toInsert);
          allInserted.push(...dbResult.insertedWaybills);
          allUpdated.push(...dbResult.updatedWaybills);
        }
      }
      
      pageCount++;
    }

    // 处理完所有分页后，检查缓冲区是否还有剩余数据
    if (recordsBuffer.length > 0) {
      console.log(`\n处理所有分页完毕，将剩余的 ${recordsBuffer.length} 条记录写入数据库...`);
      const dbResult = await saveToDatabase(client, recordsBuffer);
      allInserted.push(...dbResult.insertedWaybills);
      allUpdated.push(...dbResult.updatedWaybills);
      recordsBuffer = []; // 清空缓冲区
    }

  } catch (error) {
    console.error('程序运行出错:', error.message);
  } finally {
    if (client) {
      client.release();
      console.log('数据库连接已释放。');
    }
    await pool.end();
    console.log('数据库连接池已关闭。');
  }

  const endTime = Date.now();
  const totalTime = (endTime - startTime) / 1000;

  console.log('\n\n--- 所有批次处理完毕 ---');
  console.log(`总共插入 ${allInserted.length} 条记录.`);
  console.log(`总共更新 ${allUpdated.length} 条记录.`);
  const uniqueInvalid = [...new Set(allInvalid)];
  console.log(`总共发现 ${uniqueInvalid.length} 个无效或无信息的运单号.`);
  
  if (uniqueInvalid.length > 0) {
    console.log('无效/无信息运单号列表:', uniqueInvalid);
  }
  
  console.log(`\n--- 任务完成，总耗时: ${totalTime} 秒 ---`);
}

main();
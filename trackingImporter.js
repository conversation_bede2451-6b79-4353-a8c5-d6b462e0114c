const axios = require('axios');
const { Pool } = require('pg');

// 性能配置
const PERFORMANCE_CONFIG = {
  API_CONCURRENCY_LIMIT: 5, // 同时进行的API请求数量
  API_RETRY_ATTEMPTS: 3,     // API重试次数
  API_RETRY_DELAY: 1000,     // 重试延迟(ms)
  DB_BATCH_SIZE: 5000,       // 数据库批量插入大小
  PROCESSING_BATCH_SIZE: 100, // API请求批次大小(减小以提高成功率)
  DB_FETCH_SIZE: 5000,       // 数据库分页大小(减小以减少内存占用)
};

// 性能监控
class PerformanceMonitor {
  constructor() {
    this.stats = {
      apiCalls: 0,
      apiSuccesses: 0,
      apiFailures: 0,
      dbOperations: 0,
      recordsProcessed: 0,
      startTime: Date.now(),
      lastLogTime: Date.now()
    };
  }

  logApiCall(success = true) {
    this.stats.apiCalls++;
    if (success) {
      this.stats.apiSuccesses++;
    } else {
      this.stats.apiFailures++;
    }
  }

  logDbOperation() {
    this.stats.dbOperations++;
  }

  logRecordsProcessed(count) {
    this.stats.recordsProcessed += count;
  }

  getMemoryUsage() {
    const usage = process.memoryUsage();
    return {
      rss: Math.round(usage.rss / 1024 / 1024), // MB
      heapUsed: Math.round(usage.heapUsed / 1024 / 1024), // MB
      heapTotal: Math.round(usage.heapTotal / 1024 / 1024), // MB
      external: Math.round(usage.external / 1024 / 1024) // MB
    };
  }

  logProgress() {
    const now = Date.now();
    if (now - this.lastLogTime > 30000) { // 每30秒记录一次
      const elapsed = (now - this.stats.startTime) / 1000;
      const memory = this.getMemoryUsage();

      console.log(`\n=== 性能监控 (运行时间: ${Math.round(elapsed)}秒) ===`);
      console.log(`API调用: ${this.stats.apiCalls} (成功: ${this.stats.apiSuccesses}, 失败: ${this.stats.apiFailures})`);
      console.log(`数据库操作: ${this.stats.dbOperations}`);
      console.log(`已处理记录: ${this.stats.recordsProcessed}`);
      console.log(`内存使用: RSS=${memory.rss}MB, Heap=${memory.heapUsed}/${memory.heapTotal}MB`);
      console.log(`处理速度: ${Math.round(this.stats.recordsProcessed / elapsed)} 记录/秒`);
      console.log('=======================================\n');

      this.lastLogTime = now;
    }
  }

  getFinalReport() {
    const elapsed = (Date.now() - this.stats.startTime) / 1000;
    const memory = this.getMemoryUsage();

    return {
      totalTime: elapsed,
      apiCalls: this.stats.apiCalls,
      apiSuccessRate: this.stats.apiCalls > 0 ? (this.stats.apiSuccesses / this.stats.apiCalls * 100).toFixed(2) : 0,
      dbOperations: this.stats.dbOperations,
      recordsProcessed: this.stats.recordsProcessed,
      avgSpeed: Math.round(this.stats.recordsProcessed / elapsed),
      finalMemory: memory
    };
  }
}

const monitor = new PerformanceMonitor();

function safeParseDate(dateString) {
  if (!dateString) return null;
  // 专门处理带'+'号的特殊格式
  const cleanDateString = dateString.split('+')[0];
  const date = new Date(cleanDateString);
  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    return null;
  }
  return date.toISOString();
}

// API配置
const API_URL = "http://api.track.yw56.com.cn/api/tracking";
const AUTH_TOKEN = "AD153B35-22E8-4F7F-B302-895664E28B80";



// 数据库配置
const DB_CONFIG = {
  host: "hgprecn-cn-9lb36j0qc009-cn-hangzhou-vpc-st.hologres.aliyuncs.com",
  port: 80,
  database: "ywwl_holo",
  user: "LTAI5tPZDr4xh8rBQF4oSraa",
  password: "******************************",
  table: "ods_pp_track_final_node_test"
};

// 延迟函数
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 带重试的API请求
async function fetchTrackingDataWithRetry(trackingNumbers, retryCount = 0) {
  try {
    const response = await axios.get(API_URL, {
      headers: {
        Authorization: AUTH_TOKEN
      },
      params: {
        nums: trackingNumbers.join(',')
      },
      timeout: 30000 // 30秒超时
    });
    monitor.logApiCall(true);
    return response.data;
  } catch (error) {
    console.error(`API请求失败 (尝试 ${retryCount + 1}/${PERFORMANCE_CONFIG.API_RETRY_ATTEMPTS}):`, error.message);
    monitor.logApiCall(false);

    if (retryCount < PERFORMANCE_CONFIG.API_RETRY_ATTEMPTS - 1) {
      const delayTime = PERFORMANCE_CONFIG.API_RETRY_DELAY * Math.pow(2, retryCount); // 指数退避
      console.log(`等待 ${delayTime}ms 后重试...`);
      await delay(delayTime);
      return fetchTrackingDataWithRetry(trackingNumbers, retryCount + 1);
    }

    throw error;
  }
}

// 原有的fetchTrackingData函数保持兼容
async function fetchTrackingData(trackingNumbers) {
  return fetchTrackingDataWithRetry(trackingNumbers);
}

function processData(apiData, trackingNumbersInBatch) {
  const records = [];
  const invalidTrackingNumbers = [];

  if (!apiData || !apiData.result || !Array.isArray(apiData.result)) {
    console.error('无效的API响应数据');
    invalidTrackingNumbers.push(...trackingNumbersInBatch);
    return { records, invalidTrackingNumbers };
  }

  const apiTrackingNumbers = new Set(apiData.result.map(item => item.tracking_number));

  trackingNumbersInBatch.forEach(num => {
    if (!apiTrackingNumbers.has(num)) {
      invalidTrackingNumbers.push(num);
    }
  });

  apiData.result.forEach(item => {
    if (!item || !item.tracking_status || !Array.isArray(item.checkpoints) || item.checkpoints.length === 0) {
      if (item && item.tracking_number && !invalidTrackingNumbers.includes(item.tracking_number)) {
        invalidTrackingNumbers.push(item.tracking_number);
      }
      return;
    }

    const trackingStatus = item.tracking_status;

    try {
      const matchingCheckpoints = item.checkpoints.filter(
        checkpoint => checkpoint && checkpoint.tracking_status === trackingStatus
      );

      if (matchingCheckpoints.length === 0) {
        if (item.tracking_number && !invalidTrackingNumbers.includes(item.tracking_number)) {
          invalidTrackingNumbers.push(item.tracking_number);
        }
        return;
      }

      records.push({
        tracking_number: item.tracking_number || '',
        waybill_number: item.waybill_number || '',
        exchange_number: item.exchange_number || '',
        tracking_status: trackingStatus,
        tracking_node_time: safeParseDate(matchingCheckpoints[0]?.time_stamp),
        tracking_node_time_create: safeParseDate(matchingCheckpoints[0]?.time_create)
      });
    } catch (error) {
      console.error('处理跟踪项时出错:', error.message);
      if (item.tracking_number && !invalidTrackingNumbers.includes(item.tracking_number)) {
        invalidTrackingNumbers.push(item.tracking_number);
      }
    }
  });

  return { records, invalidTrackingNumbers };
}

// 优化的数据库保存函数
async function saveToDatabase(client, records) {
  if (records.length === 0) {
    return { insertedWaybills: [], updatedWaybills: [] };
  }

  const startTime = Date.now();
  console.log(`开始数据库操作，准备插入 ${records.length} 条记录...`);

  try {
    // 使用 COPY 命令进行批量插入（更高效）
    const copyText = `COPY ${DB_CONFIG.table} (tracking_number, waybill_number, exchange_number, tracking_status, tracking_node_time, tracking_node_time_create) FROM STDIN WITH (FORMAT csv)`;

    // 准备CSV格式的数据
    const csvData = records.map(r => {
      const values = [
        r.tracking_number || '',
        r.waybill_number || '',
        r.exchange_number || '',
        r.tracking_status || '',
        r.tracking_node_time || '',
        r.tracking_node_time_create || ''
      ];
      // 转义CSV特殊字符
      return values.map(v => {
        if (v === null || v === undefined) return '';
        const str = String(v);
        if (str.includes(',') || str.includes('"') || str.includes('\n')) {
          return `"${str.replace(/"/g, '""')}"`;
        }
        return str;
      }).join(',');
    }).join('\n');

    // 使用事务
    await client.query('BEGIN');

    // 执行COPY命令
    const copyStream = client.query(copyText);
    copyStream.write(csvData);
    copyStream.end();

    await client.query('COMMIT');

    const insertedWaybills = records.map(r => r.waybill_number);
    const endTime = Date.now();

    monitor.logDbOperation();
    monitor.logRecordsProcessed(insertedWaybills.length);

    console.log(`数据库操作完成: 插入 ${insertedWaybills.length} 条记录，耗时 ${(endTime - startTime) / 1000} 秒`);
    return { insertedWaybills, updatedWaybills: [] };

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('数据库COPY操作失败，回退到INSERT方式:', error.message);

    // 如果COPY失败，回退到原来的INSERT方式
    return await saveToDatabase_Fallback(client, records);
  }
}

// 回退的数据库保存函数（原来的方式）
async function saveToDatabase_Fallback(client, records) {
  try {
    await client.query('BEGIN');

    // 分批插入，避免参数过多
    const batchSize = 1000;
    let totalInserted = 0;

    for (let i = 0; i < records.length; i += batchSize) {
      const batch = records.slice(i, i + batchSize);
      const insertParams = [];
      const insertValues = batch.map((r, idx) => {
        const base = idx * 6;
        insertParams.push(
          r.tracking_number, r.waybill_number, r.exchange_number,
          r.tracking_status, r.tracking_node_time, r.tracking_node_time_create
        );
        return `($${base + 1}, $${base + 2}, $${base + 3}, $${base + 4}, $${base + 5}::timestamp, $${base + 6}::timestamp)`;
      }).join(',');

      const insertQuery = `
        INSERT INTO ${DB_CONFIG.table} (
          tracking_number, waybill_number, exchange_number,
          tracking_status, tracking_node_time, tracking_node_time_create
        ) VALUES ${insertValues};
      `;

      await client.query(insertQuery, insertParams);
      totalInserted += batch.length;
      console.log(`已插入 ${totalInserted}/${records.length} 条记录`);
    }

    await client.query('COMMIT');

    const insertedWaybills = records.map(r => r.waybill_number);
    console.log(`回退INSERT操作完成: 插入 ${insertedWaybills.length} 条记录`);
    return { insertedWaybills, updatedWaybills: [] };

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('数据库INSERT操作也失败:', error.message);
    throw error;
  }
}

// 并发控制的批处理函数
async function processBatchesConcurrently(batches) {
  const results = [];
  const totalBatches = batches.length;

  // 分组处理，每组最多CONCURRENCY_LIMIT个批次
  for (let i = 0; i < batches.length; i += PERFORMANCE_CONFIG.API_CONCURRENCY_LIMIT) {
    const batchGroup = batches.slice(i, i + PERFORMANCE_CONFIG.API_CONCURRENCY_LIMIT);

    console.log(`\n处理批次组 ${Math.floor(i / PERFORMANCE_CONFIG.API_CONCURRENCY_LIMIT) + 1}，包含 ${batchGroup.length} 个批次`);

    const groupPromises = batchGroup.map((batch, index) =>
      processBatch(batch, i + index + 1, totalBatches)
    );

    const groupResults = await Promise.allSettled(groupPromises);
    results.push(...groupResults);

    // 在批次组之间添加短暂延迟，避免API服务器过载
    if (i + PERFORMANCE_CONFIG.API_CONCURRENCY_LIMIT < batches.length) {
      await delay(500);
    }
  }

  return results;
}

async function processBatch(batch, batchNum, totalBatches) {
  const batchStartTime = Date.now();
  console.log(`\n--- 开始处理批次 ${batchNum}/${totalBatches} (运单号数量: ${batch.length}) ---`);

  try {
    const apiData = await fetchTrackingData(batch);
    const { records, invalidTrackingNumbers } = processData(apiData, batch);

    console.log(`批次 ${batchNum} API处理完成，共 ${records.length} 条有效记录`);
    const batchEndTime = Date.now();
    console.log(`--- 批次 ${batchNum} 处理完毕，耗时 ${(batchEndTime - batchStartTime) / 1000} 秒 ---`);

    return {
      records,
      invalidTrackingNumbers
    };

  } catch (error) {
    console.error(`处理批次 ${batchNum} 时出错:`, error.message);
    const batchEndTime = Date.now();
    console.log(`--- 批次 ${batchNum} 处理失败，耗时 ${(batchEndTime - batchStartTime) / 1000} 秒 ---`);
    // 返回一个包含错误信息和无效运单号的结构，以便上层可以统一处理
    return {
      records: [],
      invalidTrackingNumbers: batch, // 整个批次的运单都标记为无效
      error: error.message
    };
  }
}

async function main() {
  const startTime = Date.now();
  const dbFetchSize = PERFORMANCE_CONFIG.DB_FETCH_SIZE;
  const processingBatchSize = PERFORMANCE_CONFIG.PROCESSING_BATCH_SIZE;
  const dbInsertThreshold = PERFORMANCE_CONFIG.DB_BATCH_SIZE;
  
  // 优化内存使用：只保留统计数据，不保留具体的运单号列表
  let totalInserted = 0;
  let totalUpdated = 0;
  let totalInvalid = 0;
  let recordsBuffer = []; // 用于累积记录的缓冲区

  const pool = new Pool(DB_CONFIG);
  console.log('数据库连接池已创建。');
  let client; // 在循环外声明client

  try {
    client = await pool.connect(); // 获取一个长期连接
    console.log('数据库连接已获取。');

    let lastWaybillNumber = ''; // Keyset for pagination
    let pageCount = 1;

    while (true) {
      console.log(`\n\n---正在从数据库获取第 ${pageCount} 页运单 (LIMIT ${dbFetchSize})---`);
      
      let query;
      let queryParams;

      if (lastWaybillNumber === '') {
        // First page
        query = `SELECT waybill_number FROM public.ods_pp_track_final_node_waybill_number_test ORDER BY waybill_number LIMIT $1`;
        queryParams = [dbFetchSize];
      } else {
        // Subsequent pages using keyset pagination
        query = `SELECT waybill_number FROM public.ods_pp_track_final_node_waybill_number_test WHERE waybill_number > $1 ORDER BY waybill_number LIMIT $2`;
        queryParams = [lastWaybillNumber, dbFetchSize];
      }
      
      const result = await client.query(query, queryParams);
      const waybillsToProcess = result.rows.map(row => row.waybill_number);

      if (waybillsToProcess.length === 0) {
        console.log('数据库中已无更多运单，处理结束。');
        break; // 退出主循环
      }
      
      // 更新下一轮查询的起始点
      lastWaybillNumber = waybillsToProcess[waybillsToProcess.length - 1];

      console.log(`获取到 ${waybillsToProcess.length} 个运单号，开始分批处理...`);

      const batches = [];
      for (let i = 0; i < waybillsToProcess.length; i += processingBatchSize) {
          batches.push(waybillsToProcess.slice(i, i + processingBatchSize));
      }
      const totalBatchesInPage = batches.length;
      console.log(`本页运单将分为 ${totalBatchesInPage} 个批次进行API请求。`);

      // 使用并发控制的批处理
      const results = await processBatchesConcurrently(batches);

      for (const res of results) {
        if (res.status === 'fulfilled' && res.value) {
          const { records, invalidTrackingNumbers } = res.value;
          recordsBuffer.push(...records);
          totalInvalid += invalidTrackingNumbers.length;
        } else {
          console.error(`一个批次的Promise处理失败:`, res.reason || (res.value && res.value.error));
          if (res.value && res.value.invalidTrackingNumbers) {
            totalInvalid += res.value.invalidTrackingNumbers.length;
          }
        }

        // 检查缓冲区是否达到阈值
        if (recordsBuffer.length >= dbInsertThreshold) {
          console.log(`\n缓冲区达到 ${recordsBuffer.length} 条记录，开始写入数据库...`);
          const toInsert = recordsBuffer.splice(0, dbInsertThreshold);

          const dbResult = await saveToDatabase(client, toInsert);
          totalInserted += dbResult.insertedWaybills.length;
          totalUpdated += dbResult.updatedWaybills.length;

          // 强制垃圾回收（如果可用）
          if (global.gc) {
            global.gc();
          }
        }
      }

      // 记录进度
      monitor.logProgress();
      
      pageCount++;
    }

    // 处理完所有分页后，检查缓冲区是否还有剩余数据
    if (recordsBuffer.length > 0) {
      console.log(`\n处理所有分页完毕，将剩余的 ${recordsBuffer.length} 条记录写入数据库...`);
      const dbResult = await saveToDatabase(client, recordsBuffer);
      totalInserted += dbResult.insertedWaybills.length;
      totalUpdated += dbResult.updatedWaybills.length;
      recordsBuffer = []; // 清空缓冲区
    }

  } catch (error) {
    console.error('程序运行出错:', error.message);
  } finally {
    if (client) {
      client.release();
      console.log('数据库连接已释放。');
    }
    await pool.end();
    console.log('数据库连接池已关闭。');
  }

  const endTime = Date.now();
  const totalTime = (endTime - startTime) / 1000;

  console.log('\n\n--- 所有批次处理完毕 ---');
  console.log(`总共插入 ${totalInserted} 条记录.`);
  console.log(`总共更新 ${totalUpdated} 条记录.`);
  console.log(`总共发现 ${totalInvalid} 个无效或无信息的运单号.`);

  // 性能报告
  const report = monitor.getFinalReport();
  console.log('\n=== 最终性能报告 ===');
  console.log(`总耗时: ${report.totalTime.toFixed(2)} 秒`);
  console.log(`API调用: ${report.apiCalls} 次 (成功率: ${report.apiSuccessRate}%)`);
  console.log(`数据库操作: ${report.dbOperations} 次`);
  console.log(`处理记录: ${report.recordsProcessed} 条`);
  console.log(`平均速度: ${report.avgSpeed} 记录/秒`);
  console.log(`最终内存: RSS=${report.finalMemory.rss}MB, Heap=${report.finalMemory.heapUsed}MB`);
  console.log('====================');

  console.log(`\n--- 任务完成，总耗时: ${totalTime} 秒 ---`);
}

main();